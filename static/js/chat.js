const { createApp, ref, computed, onMounted, nextTick } = Vue;

createApp({
    setup() {
        // 用户信息
        const currentUser = ref('');
        const token = ref('');
        
        // 侧边栏状态
        const isSidebarCollapsed = ref(false);
        
        // 对话状态
        const conversations = ref([]);
        const currentConversationId = ref(null);
        const messages = ref([]);
        // 新添加：标记是否为临时对话（尚未在后端创建）
        const isTemporaryConversation = ref(false);

        // 计算属性：分组对话
        const groupedConversations = computed(() => {
            console.log('Computing groupedConversations, conversations count:', conversations.value.length);

            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

            const groups = [
                { title: '置顶对话', conversations: [] },
                { title: '今天', conversations: [] },
                { title: '最近一周', conversations: [] },
                { title: '最近30天', conversations: [] },
                { title: '更早', conversations: [] }
            ];

            conversations.value.forEach(conversation => {
                console.log(`Processing conversation ${conversation.id}: sticky=${conversation.sticky_flag}, updated=${conversation.updated_at}`);
                const updatedAt = new Date(conversation.updated_at);

                // 置顶对话优先
                if (conversation.sticky_flag) {
                    groups[0].conversations.push(conversation);
                } else if (updatedAt >= today) {
                    groups[1].conversations.push(conversation);
                } else if (updatedAt >= weekAgo) {
                    groups[2].conversations.push(conversation);
                } else if (updatedAt >= monthAgo) {
                    groups[3].conversations.push(conversation);
                } else {
                    groups[4].conversations.push(conversation);
                }
            });

            // 只返回有对话的分组
            const result = groups.filter(group => group.conversations.length > 0);
            console.log('Grouped conversations result:', result);
            return result;
        });
        
        // 菜单状态
        const openConversationMenuId = ref(null);
        
        // 消息状态
        const newMessage = ref('');
        const isSending = ref(false);
        const isReceiving = ref(false);
        
        // DOM引用
        const messagesContainer = ref(null);
        const messageInput = ref(null);
        
        // 格式化时间
        const formatTime = (timestamp) => {
            const date = new Date(timestamp);
            return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        };
        
        // 切换侧边栏
        const toggleSidebar = () => {
            isSidebarCollapsed.value = !isSidebarCollapsed.value;
        };
        
        // 切换对话菜单
        const toggleConversationMenu = (conversationId) => {
            if (openConversationMenuId.value === conversationId) {
                openConversationMenuId.value = null;
            } else {
                openConversationMenuId.value = conversationId;
            }
        };
        
        // 点击其他地方关闭菜单
        const handleClickOutside = (event) => {
            if (openConversationMenuId.value) {
                // 检查点击的元素是否在菜单内部
                const menuElements = document.querySelectorAll('.conversation-menu');
                let isClickInsideMenu = false;
                
                menuElements.forEach(element => {
                    if (element.contains(event.target)) {
                        isClickInsideMenu = true;
                    }
                });
                
                // 如果点击不在菜单内部，则关闭菜单
                if (!isClickInsideMenu) {
                    openConversationMenuId.value = null;
                }
            }
        };
        
        // 置顶对话
        const pinConversation = async (conversationId) => {
            console.log('pinConversation called with ID:', conversationId);
            openConversationMenuId.value = null;

            try {
                // 找到要置顶的对话
                const conversation = conversations.value.find(c => c.id === conversationId);
                if (!conversation) {
                    console.error('Conversation not found:', conversationId);
                    alert('对话不存在');
                    return;
                }

                console.log('Current conversation:', conversation);

                // 切换置顶状态
                const newStickyFlag = !conversation.sticky_flag;
                console.log('Toggling sticky flag to:', newStickyFlag);

                const response = await fetch(`/api/conversations/${conversationId}/sticky`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token.value}`
                    },
                    body: JSON.stringify({
                        sticky_flag: newStickyFlag
                    })
                });

                console.log('API response status:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('API response data:', result);

                    // 根据标准响应格式处理数据
                    if (result.code === 200) {
                        // 显示成功消息
                        console.log(`置顶状态已${newStickyFlag ? '开启' : '关闭'}`);

                        // 重新加载对话列表以获取最新状态
                        await loadConversations();
                        console.log('Conversations reloaded');
                    } else {
                        console.error('Failed to update sticky status:', result.message);
                        alert('更新置顶状态失败: ' + result.message);
                    }
                } else {
                    const errorText = await response.text();
                    console.error('Failed to update sticky status:', response.status, errorText);
                    alert('更新置顶状态失败');
                }
            } catch (error) {
                console.error('Pin conversation error:', error);
                alert('操作失败：' + error.message);
            }
        };
        
        // 处理登出
        const handleLogout = async () => {
            await apiService.logout();
            window.location.href = '/static/login.html';
        };
        
        // 加载对话列表
        const loadConversations = async () => {
            try {
                console.log('Loading conversations...');
                const result = await apiService.getConversations();

                if (result.success) {
                    const data = result.conversations;
                    console.log('Processed data:', data);

                    conversations.value = data.map(conv => ({
                        id: conv.id,
                        title: conv.title || `对话 ${conv.id}`,
                        preview: '暂无消息',
                        created_at: conv.created_at,
                        updated_at: conv.updated_at || conv.created_at,
                        sticky_flag: conv.sticky_flag || false
                    }));

                    console.log('Updated conversations.value:', conversations.value);

                    // 如果有对话，默认选择第一个
                    if (data.length > 0 && !currentConversationId.value) {
                        currentConversationId.value = data[0].id;
                        await loadMessages(data[0].id);
                    }
                } else {
                    console.error('Failed to load conversations:', result.message);
                }
            } catch (error) {
                console.error('Load conversations error:', error);
            }
        };
        
        // 创建新对话（前端临时对话）
        const createNewConversation = () => {
            // 检查是否已经存在临时对话
            const existingTempConversation = conversations.value.find(conv => 
                conv.id.toString().startsWith('temp_') && 
                currentConversationId.value === conv.id
            );
            
            // 如果当前对话已经是临时对话，则不创建新的
            if (existingTempConversation) {
                return;
            }
            
            // 创建临时对话（不调用后端API）
            const tempId = 'temp_' + Date.now();
            const tempConversation = {
                id: tempId,
                title: '新对话',
                preview: '暂无消息',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                sticky_flag: false
            };

            // 添加到对话列表顶部
            conversations.value.unshift(tempConversation);
            
            // 选择这个临时对话
            selectConversation(tempId);
            
            // 标记为临时对话
            isTemporaryConversation.value = true;
        };
        
        // 选择对话
        const selectConversation = async (conversationId) => {
            if (currentConversationId.value === conversationId) return;
            
            console.log('Selecting conversation:', conversationId);
            currentConversationId.value = conversationId;
            messages.value = [];
            
            // 如果是临时对话，不需要加载消息
            if (conversationId.toString().startsWith('temp_')) {
                console.log('Selected temporary conversation, not loading messages');
                isTemporaryConversation.value = true;
            } else {
                console.log('Selected real conversation, loading messages');
                isTemporaryConversation.value = false;
                await loadMessages(conversationId);
            }
        };
        
        // 删除对话
        const deleteConversation = async (conversationId) => {
            if (conversations.value.length <= 1) {
                alert('至少保留一个对话');
                openConversationMenuId.value = null;
                return;
            }

            if (!confirm('确定要删除这个对话吗？')) {
                openConversationMenuId.value = null;
                return;
            }

            try {
                const result = await apiService.deleteConversation(conversationId);

                if (result.success) {
                    // 从列表中移除
                    const index = conversations.value.findIndex(c => c.id === conversationId);
                    if (index !== -1) {
                        conversations.value.splice(index, 1);
                    }

                    // 如果删除的是当前对话，切换到第一个对话
                    if (currentConversationId.value === conversationId) {
                        if (conversations.value.length > 0) {
                            selectConversation(conversations.value[0].id);
                        } else {
                            currentConversationId.value = null;
                            messages.value = [];
                        }
                    }
                } else {
                    alert(result.message || '删除失败');
                }
            } catch (error) {
                console.error('Delete conversation error:', error);
                alert('删除失败');
            } finally {
                openConversationMenuId.value = null;
            }
        };
        
        // 加载消息
        const loadMessages = async (conversationId) => {
            console.log('Loading messages for conversation:', conversationId);
            try {
                const result = await apiService.getMessages(conversationId);
                console.log('API response for messages:', result);

                if (result.success) {
                    console.log('Messages loaded successfully:', result.messages);
                    // 消息现在直接是一个数组
                    messages.value = result.messages || [];
                } else {
                    console.error('Failed to load messages:', result.message);
                }
                scrollToBottom();
            } catch (error) {
                console.error('Load messages error:', error);
            }
        };
        
        // 切换所有引用的展开/折叠状态
        const toggleReferences = (message) => {
            const newState = !message.referencesExpanded;
            message.referencesExpanded = newState;
            
            // 同时控制所有引用项的展开状态
            if (message.references) {
                message.references.forEach(ref => {
                    ref.expanded = newState;
                });
            }
        };
        
        // 切换单个引用的展开/折叠状态
        const toggleReference = (reference) => {
            reference.expanded = !reference.expanded;
        };
        
        // 截断文本
        const truncateText = (text, maxLength) => {
            if (!text || text.length <= maxLength) {
                return text;
            }
            return text.substring(0, maxLength) + '...';
        };
        
        // 渲染Markdown内容
        const renderMarkdown = (content) => {
            if (!content) return '';
            // 使用marked或其他Markdown渲染库
            return marked(content);
        };
        
        // 发送消息
        const sendMessage = async () => {
            const message = newMessage.value.trim();
            if (!message || isSending.value) return;

            newMessage.value = '';

            // 添加用户消息到界面
            const userMessage = {
                role: 'user',
                content: message,
                timestamp: new Date().toISOString()
            };
            messages.value.push(userMessage);

            scrollToBottom();

            isSending.value = true;
            isReceiving.value = true;

            // 添加AI消息占位符
            const aiMessageIndex = messages.value.length;
            messages.value.push({
                role: 'assistant',
                content: '',
                timestamp: new Date().toISOString()
            });

            let assistantResponse = '';

            try {
                let conversationId = currentConversationId.value;
                
                // 如果是临时对话，需要先创建对话
                if (isTemporaryConversation.value) {
                    const result = await apiService.createConversation("新对话");
                    if (result.success) {
                        const newConversation = result.conversation;
                        
                        // 更新对话列表中的临时对话为真实对话
                        const tempIndex = conversations.value.findIndex(c => c.id === conversationId);
                        if (tempIndex !== -1) {
                            conversations.value[tempIndex] = {
                                id: newConversation.id,
                                title: newConversation.title || `对话 ${newConversation.id}`,
                                preview: '暂无消息',
                                created_at: newConversation.created_at,
                                updated_at: newConversation.updated_at || newConversation.created_at,
                                sticky_flag: newConversation.sticky_flag || false
                            };
                        }
                        
                        // 更新当前对话ID
                        conversationId = newConversation.id;
                        currentConversationId.value = newConversation.id;
                        
                        // 标记为非临时对话
                        isTemporaryConversation.value = false;
                    } else {
                        console.error('Failed to create conversation:', result.message);
                        throw new Error('创建对话失败: ' + result.message);
                    }
                }

                await apiService.sendStreamMessage(
                    conversationId,
                    message,
                    {
                        onStart: () => {
                            console.log('开始接收流式响应');
                        },
                        onData: (content, parsedData) => {
                            if (parsedData && parsedData.data) {
                                const aiMessage = messages.value[aiMessageIndex];
                                
                                // 如果返回的是引用信息
                                if (parsedData.data.references) {
                                    aiMessage.references = parsedData.data.references || [];
                                    aiMessage.referencesExpanded = false;
                                    
                                    // 初始化每个引用的展开状态
                                    if (aiMessage.references) {
                                        aiMessage.references.forEach(ref => {
                                            ref.expanded = false;
                                        });
                                    }
                                } 
                                // 如果返回的是内容片段
                                else if (parsedData.data.content !== undefined) {
                                    assistantResponse += parsedData.data.content;
                                    aiMessage.content = assistantResponse;
                                }
                                
                                scrollToBottom();
                            }
                        },
                        onError: (errorMessage, error) => {
                            messages.value[aiMessageIndex].content = `错误: ${errorMessage}`;
                            isReceiving.value = false;
                        },
                        onComplete: () => {
                            isReceiving.value = false;
                            console.log('流式响应完成');
                        }
                    }
                );
            } catch (error) {
                console.error('Send message error:', error);
                messages.value.push({
                    role: 'assistant',
                    content: `错误: ${error.message}`,
                    timestamp: new Date().toISOString()
                });
                isReceiving.value = false;
            } finally {
                isSending.value = false;
                scrollToBottom();
            }
        };
        
        // 处理键盘事件
        const handleKeyDown = (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        };
        
        // 滚动到底部
        const scrollToBottom = () => {
            nextTick(() => {
                if (messagesContainer.value) {
                    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
                }
            });
        };
        
        // 检查认证状态
        const checkAuth = () => {
            if (!apiService.isAuthenticated()) {
                window.location.href = '/static/login.html';
                return false;
            }

            token.value = apiService.token;
            currentUser.value = apiService.getCurrentUser();
            return true;
        };
        
        // 页面加载时检查认证状态并加载数据
        onMounted(() => {
            if (checkAuth()) {
                loadConversations();
                
                // 监听点击事件，用于关闭菜单
                document.addEventListener('click', handleClickOutside);
            }
        });
        
        return {
            // 数据
            currentUser,
            isSidebarCollapsed,
            conversations,
            currentConversationId,
            messages,
            openConversationMenuId,
            newMessage,
            isSending,
            isReceiving,
            messagesContainer,
            messageInput,
            
            // 计算属性
            groupedConversations,
            
            // 方法
            formatTime,
            toggleSidebar,
            toggleConversationMenu,
            pinConversation,
            handleLogout,
            createNewConversation,
            selectConversation,
            deleteConversation,
            sendMessage,
            handleKeyDown,
            toggleReferences,
            toggleReference,
            truncateText,
            renderMarkdown
        };
    }
}).mount('#app');