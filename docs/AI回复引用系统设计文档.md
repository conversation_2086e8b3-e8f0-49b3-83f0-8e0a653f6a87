# AI回复引用系统设计文档

## 1. 概述

本文档详细描述了AI回复系统中引用内容的设计与实现方案。系统需要支持：
1. AI回复需要返回引用的内容
2. 引用内容前端用markdown格式显示，可以折叠和展开，折叠时为一行
3. AI回复内容同样以markdown格式显示

## 2. 系统架构

### 2.1 整体架构图

```mermaid
graph TB
    A[用户发送消息] --> B[/chat/stream接口]
    B --> C[MessageService.generate_chat_response]
    C --> D[LLMService.generate_rag_response_with_references]
    D --> E[RAGRetriever检索相关文档]
    E --> F[格式化引用内容]
    F --> G[发送引用数据包]
    G --> H[流式发送AI回复内容]
    H --> I[前端接收并处理]
    I --> J[显示引用内容区域]
    I --> K[显示AI回复内容]
```

### 2.2 数据流设计

#### 2.2.1 流式响应数据格式

**第一个数据包（引用信息）：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "type": "references",
    "references": [
      {
        "id": 1,
        "title": "参考资料标题",
        "content": "引用内容的markdown格式文本",
        "source": "来源信息",
        "score": 0.95,
        "metadata": {
          "filename": "文档名称.pdf",
          "version": "v1.0",
          "page": 15
        }
      }
    ]
  }
}
```

**后续数据包（AI回复内容）：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "type": "content",
    "content": "AI回复的文本片段"
  }
}
```

## 3. 后端实现

### 3.1 数据模型扩展

#### 3.1.1 引用内容Schema

```python
# schemas/reference.py
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

class ReferenceItem(BaseModel):
    """单个引用项Schema"""
    id: int = Field(..., description="引用ID")
    title: str = Field(..., description="引用标题")
    content: str = Field(..., description="引用内容（Markdown格式）")
    source: Optional[str] = Field(None, description="来源信息")
    score: float = Field(..., description="相关度得分")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")

class ReferencesData(BaseModel):
    """引用数据Schema"""
    type: str = Field(default="references", description="数据类型")
    references: List[ReferenceItem] = Field(..., description="引用列表")

class ContentData(BaseModel):
    """内容数据Schema"""
    type: str = Field(default="content", description="数据类型")
    content: str = Field(..., description="内容片段")

class StreamResponse(BaseModel):
    """流式响应Schema"""
    code: int = Field(default=200, description="响应码")
    message: str = Field(default="success", description="响应消息")
    data: Union[ReferencesData, ContentData] = Field(..., description="响应数据")
```

### 3.2 服务层实现

#### 3.2.1 LLM服务扩展

```python
# services/llm_service.py
import json
from typing import Generator, List, Dict
from langchain.schema import Document

class LLMService:
    def generate_rag_response_with_references(
        self, 
        message: str, 
        collection_name: str, 
        input: Dict = None
    ) -> Generator[str, None, None]:
        """
        生成带引用内容的RAG回复（流式）
        """
        try:
            # 1. 创建RAG检索器并获取相关文档
            retriever = RAGRetriever(collection_name, input)
            documents = retriever.get_relevant_documents(message)
            
            # 2. 格式化引用内容并发送
            if documents:
                references = self._format_references(documents)
                references_data = {
                    "code": 200,
                    "message": "success",
                    "data": {
                        "type": "references",
                        "references": references
                    }
                }
                yield f"data: {json.dumps(references_data, ensure_ascii=False)}\n\n"
            
            # 3. 生成并流式发送AI回复内容
            context = self._format_context(documents)
            rag_chain = self._create_rag_chain(context)
            
            for chunk in rag_chain.stream(message):
                if chunk:
                    content_data = {
                        "code": 200,
                        "message": "success",
                        "data": {
                            "type": "content",
                            "content": chunk
                        }
                    }
                    yield f"data: {json.dumps(content_data, ensure_ascii=False)}\n\n"
                    
        except Exception as e:
            logger.error(f"生成回复失败: {str(e)}")
            error_data = {
                "code": 1005,
                "message": "内部服务器错误",
                "data": {
                    "type": "error",
                    "error": str(e)
                }
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
    
    def _format_references(self, documents: List[Document]) -> List[Dict]:
        """格式化引用内容为前端所需格式"""
        references = []
        for i, doc in enumerate(documents, 1):
            metadata = doc.metadata
            references.append({
                "id": i,
                "title": metadata.get("title", f"参考资料 {i}"),
                "content": self._format_reference_content(doc.page_content),
                "source": metadata.get("filename", "未知来源"),
                "score": metadata.get("score", 0.0),
                "metadata": {
                    "filename": metadata.get("filename", ""),
                    "version": metadata.get("version", ""),
                    "page": metadata.get("page", 0)
                }
            })
        return references
    
    def _format_reference_content(self, content: str) -> str:
        """将引用内容格式化为Markdown格式"""
        # 清理和格式化内容
        lines = content.strip().split('\n')
        formatted_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                # 如果是标题行，添加markdown标题格式
                if line.endswith(':') or line.isupper():
                    formatted_lines.append(f"### {line}")
                else:
                    formatted_lines.append(line)
            else:
                formatted_lines.append("")
        
        return '\n'.join(formatted_lines)
```

#### 3.2.2 消息服务更新

```python
# services/message_service.py
class MessageService:
    def generate_chat_response(self, chat_data: ChatRequest) -> Generator[str, None, None]:
        """
        生成流式聊天回复（集成RAG，包含引用内容）
        """
        try:
            llm_service = get_llm_service()
            yield from llm_service.generate_rag_response_with_references(
                message=chat_data.message,
                collection_name=chat_data.collection_name,
                input=chat_data.input
            )
        except Exception as e:
            logger.error(f"生成回复失败: {str(e)}")
            error_data = {
                "code": 1005,
                "message": "内部服务器错误",
                "data": {
                    "type": "error",
                    "error": "抱歉，服务暂时不可用，请稍后重试。"
                }
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
```

### 3.3 API接口更新

#### 3.3.1 流式接口保持不变

现有的 `/chat/stream` 接口无需修改，只需要确保正确处理新的数据格式：

```python
# api/chat.py
@router.post("/chat/stream", summary="发送消息（SSE流返回）")
async def chat_stream(
    chat_data: ChatRequest,
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session),
):
    """发送消息并以SSE流的形式返回AI回复"""
    
    async def generate_response():
        try:
            # 保存用户消息
            user_message = message_service.create_user_message(
                db, chat_data.conversation_id, chat_data.message, 
                current_username, chat_data.parent_msg_id
            )
            
            # 生成AI回复（包含引用）
            ai_response_content = ""
            for chunk in message_service.generate_chat_response(chat_data):
                # 直接转发从服务层返回的格式化数据
                yield chunk
                
                # 如果是内容数据，累积用于保存
                try:
                    data = json.loads(chunk.replace("data: ", ""))
                    if data.get("data", {}).get("type") == "content":
                        ai_response_content += data["data"]["content"]
                except:
                    pass
            
            # 保存AI回复到数据库
            if ai_response_content:
                message_service.create_assistant_message(
                    db, chat_data.conversation_id, ai_response_content, user_message.id
                )
            
            yield "data: [DONE]\n\n"
            
        except Exception as e:
            logger.error(f"处理聊天请求时发生错误: {str(e)}")
            error_data = json.dumps({
                "code": 1005,
                "message": "内部服务器错误",
                "data": {"type": "error", "error": str(e)}
            }, ensure_ascii=False)
            yield f'data: {error_data}\n\n'
            yield "data: [DONE]\n\n"
    
    return StreamingResponse(
        generate_response(),
        media_type="text/plain",
        headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
    )
```

## 4. 前端实现

### 4.1 数据处理逻辑

#### 4.1.1 API服务更新

```javascript
// static/js/api-service.js
class ApiService {
    async sendStreamMessage(conversationId, message, callbacks = {}) {
        const {
            onStart = () => {},
            onData = () => {},
            onError = () => {},
            onComplete = () => {}
        } = callbacks;

        try {
            await this.getAuthApi().stream('/api/chat/stream', {
                conversation_id: conversationId,
                message: message,
                collection_name: 'FinancialResearchOffice',
                input: {}
            }, {
                onStart,
                onData: (content, parsedData) => {
                    // 处理不同类型的数据
                    if (parsedData && parsedData.data) {
                        const dataType = parsedData.data.type;
                        
                        if (dataType === 'references') {
                            // 处理引用数据
                            onData(null, parsedData, 'references');
                        } else if (dataType === 'content') {
                            // 处理内容数据
                            onData(parsedData.data.content, parsedData, 'content');
                        } else if (dataType === 'error') {
                            // 处理错误数据
                            onError(parsedData.data.error, parsedData);
                        }
                    }
                },
                onError,
                onComplete
            });
        } catch (error) {
            onError(error.message || '发送消息失败', error);
        }
    }
}
```
