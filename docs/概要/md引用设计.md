# MD引用设计文档

## 1. 概述

本文档旨在详细说明如何在AI回复中实现引用内容的显示功能。根据需求，AI回复需要包含引用的内容，这些引用内容在前端将以Markdown格式显示，并支持折叠和展开功能。折叠时显示为一行，展开时显示完整内容。

当前/chat/stream接口以流式方式返回数据，每个数据片段都遵循标准格式：
```json
{
  "code": 200,
  "message": "success", 
  "data": {"content": "内容片段"}
}
```

本文档将基于该接口特性设计引用内容的显示方案。

## 2. 功能需求

### 2.1 核心功能
1. AI回复中包含引用内容
2. 引用内容以Markdown格式显示
3. 引用内容支持折叠/展开交互
4. 折叠状态下显示为单行
5. 展开状态下显示完整内容

### 2.2 显示要求
1. AI回复内容同样以Markdown格式显示
2. 引用内容与AI回复内容分离显示
3. 引用内容区域有明确的视觉标识

## 3. 设计方案

### 3.1 后端数据结构设计

由于/chat/stream接口是流式输出，我们需要在流开始时发送引用信息，然后在流式传输过程中发送AI回复内容。

#### 3.1.1 初始数据包（包含引用信息）
在流开始时发送一个包含引用信息的数据包：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "references": [
      {
        "id": 1,
        "title": "人工智能简介",
        "content": "人工智能（Artificial Intelligence），英文缩写为AI。它是研究、开发用于模拟、延伸和扩展人的智能的理论、方法、技术及应用系统的一门新的技术科学。",
        "url": "https://example.com/ai-intro"
      },
      {
        "id": 2,
        "title": "机器学习基础",
        "content": "机器学习是人工智能的一个子集，它使计算机能够从数据中学习并做出决策或预测。",
        "url": "https://example.com/ml-basics"
      }
    ]
  }
}
```

#### 3.1.2 流式内容数据包
后续数据包继续传输AI回复内容：

```json
{
  "code": 200,
  "message": "success", 
  "data": {"content": "内容片段"}
}
```

#### 3.1.3 结束标记
流结束时发送结束标记：

```json
data: [DONE]
```

### 3.2 前端显示设计

#### 3.2.1 整体布局
```
+-------------------------------------------------------------+
| AI回复内容（Markdown格式）                                  |
|                                                             |
| 这里是AI的回复内容，支持Markdown格式显示...                 |
|                                                             |
+-------------------------------------------------------------+
| [参考资料] [▼]                                              |
+-------------------------------------------------------------+
| 引用内容1（折叠状态下显示单行）                             |
+-------------------------------------------------------------+
| 引用内容2（折叠状态下显示单行）                             |
+-------------------------------------------------------------+
```

#### 3.2.2 折叠状态
- 默认状态下，引用内容区域处于折叠状态
- 每个引用内容仅显示标题或内容的前N个字符，以单行形式展示
- 右上角显示展开/折叠图标（▼/▲）

#### 3.2.3 展开状态
- 用户点击参考资料区域或展开按钮后，引用内容完全展开
- 显示引用内容的完整信息，包括标题和内容
- 支持Markdown格式渲染
- 右上角显示折叠按钮（▲）

### 3.3 交互设计

#### 3.3.1 展开/折叠操作
1. 点击"参考资料"标题区域可切换折叠/展开状态
2. 点击每个引用条目前的展开/折叠图标可单独控制该条目
3. 提供"展开全部"/"折叠全部"按钮

#### 3.3.2 引用内容操作
1. 点击引用标题可跳转到原始链接（如果有URL）
2. 支持复制引用内容
3. 提供引用内容的分享功能

### 3.4 样式设计

#### 3.4.1 引用区域样式
```css
.ai-reference-container {
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  margin-top: 1rem;
  background-color: #f8fafc;
}

.ai-reference-header {
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  border-bottom: 1px solid #e2e8f0;
}

.ai-reference-content {
  padding: 1rem;
}

.ai-reference-item {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: white;
  border-radius: 0.25rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.ai-reference-item.collapsed {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ai-reference-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.ai-reference-text {
  color: #64748b;
  font-size: 0.9rem;
}
```

#### 3.4.2 图标设计
- 折叠状态：▼ (fa-chevron-down)
- 展开状态：▲ (fa-chevron-up)
- 链接图标：↗ (fa-external-link-alt)

## 4. 实现方案

### 4.1 后端实现

#### 4.1.1 修改LLM服务
在[LLMService](file:///D:/code/chatbot-ui/chatbot/backend/services/llm_service.py#L16-L116)中添加新的方法来获取带引用的回复：

```python
def generate_rag_response_with_references(self, message: str, collection_name: str, input: Dict = None) -> Generator[str, None, None]:
    """
    生成带引用内容的RAG回复（流式）
    """
    try:
        # 创建RAG检索器
        retriever = RAGRetriever(collection_name=collection_name, input=input)
        
        # 获取相关文档
        documents = retriever.get_relevant_documents(message)
        
        # 格式化引用内容
        references = self._format_references(documents)
        
        # 发送引用信息作为第一个数据包
        initial_data = {
            "code": 200,
            "message": "success",
            "data": {
                "references": references
            }
        }
        yield json.dumps(initial_data, ensure_ascii=False)
        
        # 构建系统提示模板
        system_prompt = """你是一个专业的智能助手，请根据以下参考资料回答用户的问题。

参考资料：
{context}

请根据上述参考资料回答用户问题。回答时请注意：
1. 优先使用参考资料中的信息
2. 如果参考资料不足以回答问题，请直接回复：无法检索到相关内容，请换个方式再提问试试吧！
3. 保持回答的准确性和完整性
4. 适当引用资料来源
5. 使用简洁明了的语言

用户问题：{question}

回答："""

        prompt_template = ChatPromptTemplate.from_template(system_prompt)
        
        # 构建RAG链
        rag_chain = (
                {
                    "context": lambda x: self._format_context(documents),
                    "question": RunnablePassthrough(),
                }
                | prompt_template
                | self.llm
                | StrOutputParser()
        )
        
        # 流式生成回复内容
        for chunk in rag_chain.stream(message):
            if chunk:
                content_data = {
                    "code": 200,
                    "message": "success",
                    "data": {"content": chunk}
                }
                yield json.dumps(content_data, ensure_ascii=False)

    except Exception as e:
        logger.error(f"生成带引用回复失败: {str(e)}")
        error_data = {
            "code": 1005,
            "message": "内部服务器错误",
            "data": {"error": f"生成回复时发生错误: {str(e)}"}
        }
        yield json.dumps(error_data, ensure_ascii=False)

def _format_references(self, documents: List[Document]) -> List[Dict]:
    """
    格式化引用内容
    """
    references = []
    for i, doc in enumerate(documents, 1):
        references.append({
            "id": i,
            "title": doc.metadata.get("title", f"参考资料 {i}"),
            "content": doc.page_content,
        })
    return references
```

#### 4.1.2 修改消息服务
修改[MessageService](file:///D:/code/chatbot-ui/chatbot/backend/services/message_service.py#L13-L117)中的[generate_chat_response](file:///D:/code/chatbot-ui/chatbot/backend/services/message_service.py#L64-L70)方法，使用新的带引用功能的方法：

```python
def generate_chat_response(self, chat_data: ChatRequest) -> Generator[str, None, None]:
    """
    生成流式聊天回复（集成RAG，包含引用内容）
    """
    try:
        # 使用LLM服务生成带引用的RAG回复
        llm_service = get_llm_service()
        yield from llm_service.generate_rag_response_with_references(
            message=chat_data.message,
            collection_name=chat_data.collection_name,
            input=chat_data.input
        )
    except Exception as e:
        error_message = "抱歉，RAG服务暂时不可用！"
        logger.error(f"生成回复失败: {str(e)}")
        import time
        words = error_message.split()
        for word in words:
            error_data = {
                "code": 1005,
                "message": "内部服务器错误",
                "data": {"error": error_message}
            }
            yield json.dumps(error_data, ensure_ascii=False)
            time.sleep(0.1)
```

### 4.2 前端实现

#### 4.2.1 修改消息显示组件
更新聊天界面中消息显示部分，支持引用内容的展示：

```html
<!-- AI消息模板 -->
<div v-if="message.role === 'assistant'" class="mr-auto bg-gray-100 text-gray-800 rounded-2xl rounded-bl-none px-4 py-3 shadow-sm" style="width: fit-content;">
  <!-- AI回复内容 -->
  <div class="whitespace-pre-wrap markdown-content" v-html="renderMarkdown(message.content)"></div>
  
  <!-- 引用内容区域 -->
  <div v-if="message.references && message.references.length > 0" class="ai-reference-container mt-3">
    <div class="ai-reference-header" @click="toggleReferences(message)">
      <span>参考资料</span>
      <i :class="['fas', message.referencesExpanded ? 'fa-chevron-up' : 'fa-chevron-down']"></i>
    </div>
    
    <div v-show="message.referencesExpanded" class="ai-reference-content">
      <div v-for="(reference, index) in message.references" 
           :key="reference.id" 
           class="ai-reference-item"
           :class="{ 'collapsed': !reference.expanded }"
           @click="toggleReference(reference)">
        <div class="ai-reference-title">
          <a v-if="reference.url" :href="reference.url" target="_blank" class="hover:underline">
            {{ reference.title }}
            <i class="fas fa-external-link-alt text-xs ml-1"></i>
          </a>
          <span v-else>{{ reference.title }}</span>
          <i :class="['fas float-right', reference.expanded ? 'fa-chevron-up' : 'fa-chevron-down']"></i>
        </div>
        <div v-if="reference.expanded" class="ai-reference-text markdown-content" v-html="renderMarkdown(reference.content)"></div>
        <div v-else class="ai-reference-text">{{ truncateText(reference.content, 100) }}</div>
      </div>
    </div>
  </div>
  
  <div class="text-xs mt-1 opacity-70">
    {{ formatTime(message.timestamp) }}
  </div>
</div>
```

#### 4.2.2 添加相关方法
在Vue组件中添加处理引用内容的方法：

```javascript
methods: {
  // 切换所有引用的展开/折叠状态
  toggleReferences(message) {
    const newState = !message.referencesExpanded;
    message.referencesExpanded = newState;
    
    // 同时控制所有引用项的展开状态
    if (message.references) {
      message.references.forEach(ref => {
        ref.expanded = newState;
      });
    }
  },
  
  // 切换单个引用的展开/折叠状态
  toggleReference(reference) {
    reference.expanded = !reference.expanded;
  },
  
  // 截断文本
  truncateText(text, maxLength) {
    if (!text || text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength) + '...';
  },
  
  // 渲染Markdown内容
  renderMarkdown(content) {
    if (!content) return '';
    // 使用marked或其他Markdown渲染库
    return marked(content);
  }
}
```

#### 4.2.3 流式消息处理
更新流式消息处理逻辑以支持引用内容：

```javascript
// 在流式接收消息时处理引用内容
onData: (content, parsedData) => {
  if (parsedData && parsedData.data) {
    const aiMessage = messages.value[aiMessageIndex];
    
    // 如果返回的是引用信息
    if (parsedData.data.references) {
      aiMessage.references = parsedData.data.references || [];
      aiMessage.referencesExpanded = false;
      
      // 初始化每个引用的展开状态
      if (aiMessage.references) {
        aiMessage.references.forEach(ref => {
          ref.expanded = false;
        });
      }
    } 
    // 如果返回的是内容片段
    else if (parsedData.data.content !== undefined) {
      assistantResponse += parsedData.data.content;
      aiMessage.content = assistantResponse;
    }
    
    scrollToBottom();
  }
}
```

## 5. 测试方案

### 5.1 功能测试
1. 验证AI回复内容正确显示
2. 验证引用内容正确显示
3. 验证折叠/展开功能正常
4. 验证Markdown渲染正确
5. 验证URL跳转功能正常

### 5.2 兼容性测试
1. 测试不同浏览器的兼容性
2. 测试移动端显示效果
3. 测试无引用内容时的显示
4. 测试大量引用内容时的性能

### 5.3 性能测试
1. 测试引用内容渲染性能
2. 测试折叠/展开操作的响应速度
3. 测试大数据量下的内存占用

## 6. 注意事项

1. 引用内容可能较长，需要考虑性能优化
2. 需要处理Markdown渲染的安全性问题
3. 要确保在不同屏幕尺寸下的显示效果
5. 引用内容的URL需要进行安全检查
6. 需要确保流式传输过程中引用信息和内容的正确关联
7. 需要考虑网络中断等异常情况下的处理